import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { NotificationService } from "@/lib/domains/notification/notification.service"
import { sendEmail } from "@/lib/server/email-service"
import { EmailTemplates } from "@/lib/server/email-templates"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(<PERSON><PERSON>an)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

export async function GET(request: NextRequest) {
  try {
    // Validate cron secret for security
    const authHeader = request.headers.get("authorization")
    const providedSecret = authHeader?.replace("Bearer ", "")
    
    if (providedSecret !== CRON_SECRET) {
      console.error("Unauthorized cron job access attempt")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Validate origin for additional security
    const origin = request.headers.get("origin")
    const userAgent = request.headers.get("user-agent")
    
    // Allow Vercel cron jobs (they don't always have origin headers)
    const isVercelCron = userAgent?.includes("vercel") || !origin
    const isAllowedOrigin = origin && ALLOWED_ORIGINS.includes(origin)
    
    if (!isVercelCron && !isAllowedOrigin) {
      console.error("Invalid origin for cron job:", origin)
      return NextResponse.json({ error: "Invalid origin" }, { status: 403 })
    }

    console.log("Starting trip status check (activation and completion)...")

    // Get Firebase Admin instance
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      throw new Error("Firebase Admin not initialized")
    }

    // Get current date in UTC
    const now = new Date()
    const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    const tripsRef = adminDb.collection("trips")
    const activatedTrips: any[] = []
    const completedTrips: any[] = []
    const batch = adminDb.batch()

    // STEP 1: Check for trips that should become active (planning → active)
    console.log("Checking for trips to activate...")
    const planningTripsQuery = tripsRef.where("status", "==", "planning")
    const planningTripsSnapshot = await planningTripsQuery.get()

    for (const tripDoc of planningTripsSnapshot.docs) {
      const tripData = tripDoc.data()
      const tripId = tripDoc.id

      // Convert Firestore timestamp to Date
      const startDate = tripData.startDate?.toDate()
      if (!startDate) continue

      // Check if trip should be activated (startDate <= current date)
      const tripStartDate = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate()
      )

      if (tripStartDate <= currentDate) {
        console.log(`Activating trip ${tripId} (started: ${tripStartDate.toISOString()})`)

        // Update trip status to active
        const tripRef = adminDb.collection("trips").doc(tripId)
        batch.update(tripRef, {
          status: "active",
          updatedAt: now,
        })

        // Get trip attendees for notifications
        const userTripsRef = adminDb.collection("userTrips")
        const userTripsQuery = userTripsRef
          .where("tripId", "==", tripId)
          .where("status", "==", "going")
        const userTripsSnapshot = await userTripsQuery.get()

        const attendeeIds = userTripsSnapshot.docs.map((doc) => doc.data().userId)

        activatedTrips.push({
          tripId,
          tripName: tripData.name || "Unknown Trip",
          destination: tripData.destination || "Unknown Destination",
          squadId: tripData.squadId,
          attendeeIds,
        })
      }
    }

    // STEP 2: Check for trips that should be completed (active → completed)
    console.log("Checking for trips to complete...")
    const activeTripsQuery = tripsRef.where("status", "==", "active")
    const activeTripsSnapshot = await activeTripsQuery.get()

    // Process each active trip
    for (const tripDoc of activeTripsSnapshot.docs) {
      const tripData = tripDoc.data()
      const tripId = tripDoc.id

      // Convert Firestore timestamp to Date
      const endDate = tripData.endDate?.toDate()
      if (!endDate) continue

      // Check if trip should be completed (endDate < current date)
      const tripEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())

      if (tripEndDate < currentDate) {
        console.log(`Marking trip ${tripId} as completed (ended: ${tripEndDate.toISOString()})`)

        // Update trip status to completed
        const tripRef = adminDb.collection("trips").doc(tripId)
        batch.update(tripRef, {
          status: "completed",
          updatedAt: now,
        })

        // Get trip attendees for notifications
        const userTripsRef = adminDb.collection("userTrips")
        const userTripsQuery = userTripsRef
          .where("tripId", "==", tripId)
          .where("status", "==", "going")
        const userTripsSnapshot = await userTripsQuery.get()

        const attendeeIds = userTripsSnapshot.docs.map((doc) => doc.data().userId)

        completedTrips.push({
          tripId,
          tripName: tripData.name || "Unknown Trip",
          destination: tripData.destination || "Unknown Destination",
          squadId: tripData.squadId,
          attendeeIds,
        })
      }
    }

    // Commit the batch update
    const totalUpdates = activatedTrips.length + completedTrips.length
    if (totalUpdates > 0) {
      await batch.commit()
      console.log(
        `Updated ${activatedTrips.length} trips to active status and ${completedTrips.length} trips to completed status`
      )
    }

    // Send notifications for activated trips
    const activationNotificationResults = []

    for (const trip of activatedTrips) {
      try {
        // Create notifications for all trip members about trip activation
        for (const userId of trip.attendeeIds) {
          try {
            await NotificationService.createNotification(userId, {
              userId,
              type: "trip_update",
              title: "Trip Started! 🚀",
              message: `Your trip to ${trip.destination} has started! Have an amazing time and stay safe.`,
              read: false,
              actionUrl: `/trips/${trip.tripId}`,
              relatedEntityId: trip.tripId,
              relatedEntityType: "trip",
            })
          } catch (notifError) {
            console.error(`Error creating activation notification for user ${userId}:`, notifError)
          }
        }

        activationNotificationResults.push({
          tripId: trip.tripId,
          notificationsSent: trip.attendeeIds.length,
        })
      } catch (error) {
        console.error(`Error processing activation notifications for trip ${trip.tripId}:`, error)
      }
    }

    // Send notifications and emails for completed trips
    const completionNotificationResults = []
    const completionEmailResults = []

    for (const trip of completedTrips) {
      try {
        // Create notifications for all trip members
        for (const userId of trip.attendeeIds) {
          try {
            await NotificationService.createNotification(userId, {
              userId,
              type: "trip_completed",
              title: "Trip Completed! 🎉",
              message: `Your trip to ${trip.destination} has been completed. Share your experience by reviewing the trip!`,
              read: false,
              actionUrl: `/trips/${trip.tripId}/review`,
              relatedEntityId: trip.tripId,
              relatedEntityType: "trip",
            })
          } catch (notifError) {
            console.error(`Error creating notification for user ${userId}:`, notifError)
          }
        }

        completionNotificationResults.push({
          tripId: trip.tripId,
          notificationsSent: trip.attendeeIds.length,
        })

        // Get user details for email sending
        const userDocs = await Promise.all(
          trip.attendeeIds.map((userId) => adminDb.collection("users").doc(userId).get())
        )

        const users = userDocs
          .filter((doc) => doc.exists)
          .map((doc) => ({ id: doc.id, ...doc.data() }))

        // Send emails to all trip members
        for (const user of users) {
          try {
            const emailResult = await sendEmail({
              to: user.email,
              subject: "Trip Completed! Time to Review Your Experience! 🌟",
              templateId: EmailTemplates.TRIP_COMPLETED,
              params: {
                tripName: trip.tripName,
                destination: trip.destination,
                userName: user.displayName || user.email,
                reviewUrl: `${process.env.NEXT_PUBLIC_APP_URL}/trips/${trip.tripId}/review`,
              },
            })

            if (emailResult.success) {
              completionEmailResults.push({
                userId: user.id,
                email: user.email,
                messageId: emailResult.messageId,
              })
            } else {
              console.error(`Failed to send email to ${user.email}:`, emailResult.error)
            }
          } catch (emailError) {
            console.error(`Error sending email to ${user.email}:`, emailError)
          }
        }
      } catch (error) {
        console.error(`Error processing notifications/emails for trip ${trip.tripId}:`, error)
      }
    }

    const result = {
      success: true,
      timestamp: now.toISOString(),
      tripsProcessed: {
        planning: planningTripsSnapshot.size,
        active: activeTripsSnapshot.size,
      },
      tripsActivated: activatedTrips.length,
      tripsCompleted: completedTrips.length,
      activatedTrips: activatedTrips.map((t) => ({
        tripId: t.tripId,
        destination: t.destination,
        attendeeCount: t.attendeeIds.length,
      })),
      completedTrips: completedTrips.map((t) => ({
        tripId: t.tripId,
        destination: t.destination,
        attendeeCount: t.attendeeIds.length,
      })),
      activationNotificationResults,
      completionNotificationResults,
      completionEmailResults,
    }

    console.log("Trip status check completed:", result)
    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in trip status cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Also handle POST requests for manual testing
export async function POST(request: NextRequest) {
  return GET(request)
}
