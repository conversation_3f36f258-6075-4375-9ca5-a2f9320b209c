"use client"

import { useState, useTransition } from "react"
import { useR<PERSON><PERSON> } from "next/navigation"
import { Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Trip } from "@/lib/domains/trip/trip.types"
import { TRIP_REVIEW_CONSTRAINTS } from "@/lib/domains/trip-review/trip-review.types"
import { AuthService } from "@/lib/domains/auth/auth.service"
import { submitTripReview } from "../actions/submit-review"

interface TripReviewFormProps {
  trip: Trip
}

export function TripReviewForm({ trip }: TripReviewFormProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [feedback, setFeedback] = useState("")
  const [isPending, startTransition] = useTransition()
  const { toast } = useToast()
  const router = useRouter()

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      try {
        // Get auth token
        const authToken = await AuthService.getAuthToken()
        if (!authToken) {
          toast({
            title: "Error",
            description: "You must be logged in to submit a review",
            variant: "destructive",
          })
          return
        }

        const result = await submitTripReview(trip.id, authToken, formData)

        if (result.success) {
          toast({
            title: "Review Submitted!",
            description: "Thank you for sharing your experience with your squad.",
          })
          // Redirect to trip page after successful submission
          router.push(`/trips/${trip.id}`)
        } else {
          toast({
            title: "Error",
            description: result.error || "Failed to submit review",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        })
      }
    })
  }

  const handleCancel = () => {
    router.push(`/trips/${trip.id}`)
  }

  const isFormValid = rating > 0 && 
    feedback.trim().length >= TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH &&
    feedback.trim().length <= TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Share Your Experience</CardTitle>
      </CardHeader>
      <CardContent>
        <form action={handleSubmit} className="space-y-6">
          {/* Star Rating */}
          <div className="space-y-2">
            <Label htmlFor="rating">Overall Rating</Label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1 hover:scale-110 transition-transform"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= (hoveredRating || rating)
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-muted-foreground"
                    }`}
                  />
                </button>
              ))}
              <span className="ml-2 text-sm text-muted-foreground">
                {rating > 0 && (
                  <>
                    {rating} star{rating !== 1 ? "s" : ""}
                  </>
                )}
              </span>
            </div>
            <input type="hidden" name="rating" value={rating} />
          </div>

          {/* Feedback */}
          <div className="space-y-2">
            <Label htmlFor="feedback">Your Feedback</Label>
            <Textarea
              id="feedback"
              name="feedback"
              placeholder="Share your thoughts about the trip, what you enjoyed, and any suggestions for future trips..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="min-h-[120px] resize-none"
              maxLength={TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH}
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>
                Minimum {TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MIN_LENGTH} characters
              </span>
              <span>
                {feedback.length}/{TRIP_REVIEW_CONSTRAINTS.FEEDBACK_MAX_LENGTH}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              disabled={!isFormValid || isPending}
              className="flex-1"
            >
              {isPending ? "Submitting..." : "Submit Review"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isPending}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
