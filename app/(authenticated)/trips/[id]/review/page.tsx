"use client"

import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { useUserWithData } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeTrip } from "@/lib/domains/trip/trip.realtime.hooks"
import { useRealtimeUserTripReview } from "@/lib/domains/trip-review/trip-review.realtime.hooks"
import { useLoadUserTripReview } from "@/lib/domains/trip-review/trip-review.hooks"
import { UserTripService } from "@/lib/domains/user-trip/user-trip.service"
import { PageLoading } from "@/components/page-loading"
import { TripReviewForm } from "./components/trip-review-form"
import { TripReviewDisplay } from "./components/trip-review-display"

export default function TripReviewPage() {
  const params = useParams()
  const router = useRouter()
  const { user, loading: authLoading } = useUserWithData()
  const tripId = params.id as string

  const { trip, loading: tripLoading, error: tripError } = useRealtimeTrip(tripId)
  const { userReview, loading: reviewLoading } = useRealtimeUserTripReview(tripId)
  const { loadUserReview } = useLoadUserTripReview()

  const [userTripStatus, setUserTripStatus] = useState<any>(null)
  const [statusLoading, setStatusLoading] = useState(true)

  // Load user trip status
  useEffect(() => {
    const loadUserStatus = async () => {
      if (!user || !tripId) return

      try {
        setStatusLoading(true)
        const status = await UserTripService.checkUserTripStatus(user.uid, tripId)
        setUserTripStatus(status)
      } catch (error) {
        console.error("Error loading user trip status:", error)
      } finally {
        setStatusLoading(false)
      }
    }

    loadUserStatus()
  }, [user, tripId])

  // Load user review if not already loaded
  useEffect(() => {
    if (user && tripId && !userReview && !reviewLoading) {
      loadUserReview(tripId)
    }
  }, [user, tripId, userReview, reviewLoading, loadUserReview])

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login")
    }
  }, [authLoading, user, router])

  // Redirect if trip not found or user not authorized
  useEffect(() => {
    if (!tripLoading && !statusLoading && trip && userTripStatus) {
      // Check if user is a trip attendee
      if (userTripStatus.status !== "going") {
        router.push(`/trips/${tripId}`)
        return
      }

      // Check if trip is completed
      if (trip.status !== "completed") {
        router.push(`/trips/${tripId}`)
        return
      }
    }
  }, [trip, userTripStatus, tripLoading, statusLoading, router, tripId])

  // Show loading state
  if (authLoading || tripLoading || statusLoading || reviewLoading) {
    return <PageLoading message="Loading trip review..." />
  }

  // Show error state
  if (tripError) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive">Error</h1>
          <p className="text-muted-foreground mt-2">Failed to load trip data</p>
        </div>
      </div>
    )
  }

  // Don't render if we don't have the required data
  if (!user || !trip || !userTripStatus) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">Review Your Trip</h1>
          <div className="space-y-2">
            <h2 className="text-xl text-muted-foreground">{trip.destination}</h2>
            <p className="text-sm text-muted-foreground">
              {trip.startDate.toDate().toLocaleDateString()} -{" "}
              {trip.endDate.toDate().toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Review Form or Display */}
        {userReview ? (
          <TripReviewDisplay review={userReview} trip={trip} />
        ) : (
          <TripReviewForm trip={trip} />
        )}
      </div>
    </div>
  )
}
